@echo off
REM Windows批处理启动脚本
REM 用于在Windows环境中启动开发服务器

echo 🎯 Starting Huitong Material Development Environment (Windows)
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo 💡 Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM 设置环境变量
set NODE_ENV=development

REM 检查并杀死占用端口3001的进程
echo 🔍 Checking port 3001...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
    echo ⚠️  Port 3001 is in use, killing process %%a
    taskkill /F /PID %%a >nul 2>&1
)

echo ✅ Port 3001 is ready
echo.

REM 启动开发服务器
echo 🚀 Starting development servers...
echo.
npm run dev

REM 如果出现错误，暂停以便查看错误信息
if %errorlevel% neq 0 (
    echo.
    echo ❌ Development servers failed to start
    pause
)
