{"name": "huitong-material-vercel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently --kill-others-on-fail --names \"frontend,backend\" -c \"bgBlue.bold,bgGreen.bold\" \"npm:dev:frontend\" \"npm:dev:backend\"", "dev:safe": "node scripts/start-dev.js", "dev:frontend": "vite", "dev:backend": "nodemon backend/server.mjs", "build": "prisma generate && vite build", "start": "node backend/server.mjs", "start:prod": "node scripts/start-prod.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:unused": "ts-prune", "prepare": "husky install", "preview": "vite preview", "clean": "node scripts/clean.js", "clean:cache": "node scripts/clean.js --cache-only", "clean:all": "node scripts/clean.js --all", "setup": "node scripts/setup-env.js", "check:env": "node scripts/check-env.js", "check:env:simple": "node -e \"console.log('Environment check:'); console.log('NODE_ENV:', process.env.NODE_ENV); console.log('POSTGRES_PRISMA_URL:', process.env.POSTGRES_PRISMA_URL ? 'Set' : 'Not set'); console.log('BLOB_READ_WRITE_TOKEN:', process.env.BLOB_READ_WRITE_TOKEN ? 'Set' : 'Not set');\""}, "dependencies": {"@prisma/client": "^5.17.0", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "multer": "^2.0.1", "@vercel/postgres": "^0.9.0", "ali-oss": "^6.20.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^4.19.2", "lucide-react": "^0.513.0", "pg": "^8.12.0", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "three": "^0.177.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.13", "@types/node": "^20.14.10", "@types/pg": "^8.11.6", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/three": "^0.166.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^8.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "husky": "^9.0.10", "json-server": "^1.0.0-beta.3", "lint-staged": "^15.2.3", "nodemon": "^3.1.0", "prisma": "^5.17.0", "ts-prune": "^0.10.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix"]}}