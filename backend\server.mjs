import dotenv from 'dotenv';  // Cline: Restart for port 3003
// 加载环境变量前打印路径
console.log('Loading env from:', path.resolve('../.env.local'));
dotenv.config({ path: '../.env.local' });

console.log('Loading env from:', path.resolve('../.env'));
dotenv.config({ path: '../.env' });

// 打印加载的环境变量
console.log('POSTGRES_PRISMA_URL:', process.env.POSTGRES_PRISMA_URL);
console.log('POSTGRES_URL_NON_POOLING:', process.env.POSTGRES_URL_NON_POOLING);

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const app = express();
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.POSTGRES_PRISMA_URL
    }
  }
});
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, uploadsDir),
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage });

app.post('/api/upload', upload.single('file'), (req, res) => {
  if (!req.file) return res.status(400).json({ error: 'No file uploaded.' });
  res.status(200).json({ filePath: `/uploads/${req.file.filename}` });
});

app.get('/api/models', async (req, res) => {
  try {
    const models = await prisma.model.findMany({
      orderBy: { createdAt: 'desc' }
    });
    res.json(models);
  } catch (error) {
    console.error('Failed to fetch models:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) return res.status(400).json({ error: 'Invalid model ID' });

    const model = await prisma.model.findUnique({ where: { id: modelId } });
    if (!model) return res.status(404).json({ error: 'Model not found' });

    res.json(model);
  } catch (error) {
    console.error(`Failed to fetch model ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/models', async (req, res) => {
  const { name, filePath, thumbnailPath } = req.body;
  if (!name) return res.status(400).json({ error: 'Name is required' });

  try {
    const newModel = await prisma.model.create({
      data: { name, filePath: filePath || '', thumbnailPath: thumbnailPath || '' }
    });
    res.status(201).json(newModel);
  } catch (error) {
    console.error('Failed to create model:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) return res.status(400).json({ error: 'Invalid model ID' });

    const { name, filePath, thumbnailPath } = req.body;
    const data = {};
    if (name) data.name = name;
    if (filePath) data.filePath = filePath;
    if (thumbnailPath !== undefined) data.thumbnailPath = thumbnailPath;

    if (Object.keys(data).length === 0) {
      return res.status(400).json({ error: 'No update data provided' });
    }

    const updatedModel = await prisma.model.update({
      where: { id: modelId },
      data
    });
    res.json(updatedModel);
  } catch (error) {
    console.error(`Failed to update model ${req.params.id}:`, error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Model not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

const deleteFile = (filePath) => {
  if (!filePath) return;
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`Deleted file: ${fullPath}`);
    } catch (err) {
      console.error(`Error deleting file ${fullPath}:`, err);
    }
  }
};

app.delete('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) return res.status(400).json({ error: 'Invalid model ID' });

    const model = await prisma.model.findUnique({ where: { id: modelId } });
    if (!model) return res.status(404).json({ error: 'Model not found' });

    deleteFile(model.filePath);
    deleteFile(model.thumbnailPath);

    await prisma.model.delete({ where: { id: modelId } });
    res.status(204).send();
  } catch (error) {
    console.error(`Failed to delete model ${req.params.id}:`, error);
    if (error.code === 'P2025') return res.status(404).json({ error: 'Model not found' });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/materials', async (req, res) => {
  try {
    const materials = await prisma.material.findMany({
      orderBy: { createdAt: 'desc' }
    });
    res.json(materials);
  } catch (error) {
    console.error('Failed to fetch materials:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/materials', async (req, res) => {
  const { model_id, name, color, metalness, roughness, glass, thumbnailPath } = req.body;
  if (!model_id || !name) {
    return res.status(400).json({ error: 'model_id and name are required' });
  }

  try {
    const newMaterial = await prisma.material.create({
      data: {
        modelId: parseInt(model_id, 10),
        name,
        data: {
          color,
          metalness: parseFloat(metalness) || 0,
          roughness: parseFloat(roughness) || 0,
          glass: parseFloat(glass) || 0
        },
        thumbnailPath
      }
    });
    res.status(201).json(newMaterial);
  } catch (error) {
    console.error('Failed to create material:', error);
    res.status(500).json({ error: 'Failed to create material', details: error.message });
  }
});

app.delete('/api/materials/:id', async (req, res) => {
  try {
    const material = await prisma.material.findUnique({ where: { id: req.params.id } });
    if (material) deleteFile(material.thumbnailPath);

    await prisma.material.delete({ where: { id: req.params.id } });
    res.status(204).send();
  } catch (error) {
    console.error(`Failed to delete material ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default app;

if (process.env.NODE_ENV !== 'production' || !process.env.VERCEL) {
  const PORT = process.env.PORT || 3001;
  const server = app.listen(PORT, () => {
    console.log(`🚀 Backend server running on http://localhost:${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🗄️  Database: ${process.env.POSTGRES_PRISMA_URL ? 'Connected' : 'Not configured'}`);
  });

  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down');
    server.close(() => process.exit(0));
  });

  process.on('SIGINT', () => {
    console.log('🛑 SIGINT received, shutting down');
    server.close(() => process.exit(0));
  });

  server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.error(`❌ Port ${PORT} already in use`);
    } else {
      console.error('❌ Server error:', err);
    }
    process.exit(1);
  });
}
