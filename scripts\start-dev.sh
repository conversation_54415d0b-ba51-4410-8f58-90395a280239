#!/bin/bash

# Unix/Linux/macOS shell启动脚本
# 用于在Unix-like系统中启动开发服务器

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${2:-$NC}$1${NC}"
}

log "🎯 Starting Huitong Material Development Environment (Unix)" "$BOLD"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    log "❌ Node.js is not installed or not in PATH" "$RED"
    log "💡 Please install Node.js from https://nodejs.org/" "$YELLOW"
    exit 1
fi

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    log "❌ npm is not available" "$RED"
    exit 1
fi

log "✅ Node.js $(node --version) and npm $(npm --version) are available" "$GREEN"
echo

# 设置环境变量
export NODE_ENV=development

# 检查并杀死占用端口3001的进程
log "🔍 Checking port 3001..." "$CYAN"

if lsof -ti:3001 >/dev/null 2>&1; then
    log "⚠️  Port 3001 is in use, killing processes..." "$YELLOW"
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    
    # 等待端口释放
    sleep 2
    
    if lsof -ti:3001 >/dev/null 2>&1; then
        log "❌ Failed to free port 3001" "$RED"
        exit 1
    fi
fi

log "✅ Port 3001 is ready" "$GREEN"
echo

# 检查环境变量
log "🔧 Checking environment variables..." "$CYAN"

if [ -z "$POSTGRES_PRISMA_URL" ] && [ ! -f ".env" ] && [ ! -f ".env.local" ]; then
    log "⚠️  No environment variables found" "$YELLOW"
    log "💡 Please create .env or .env.local file with required variables" "$YELLOW"
fi

# 检查依赖
log "📦 Checking dependencies..." "$CYAN"

if [ ! -d "node_modules" ]; then
    log "⚠️  Dependencies not found, installing..." "$YELLOW"
    npm install
fi

log "✅ Dependencies are ready" "$GREEN"
echo

# 启动开发服务器
log "🚀 Starting development servers..." "$CYAN"
echo

# 优雅关闭处理
trap 'log "\n🛑 Shutting down development servers..." "$YELLOW"; exit 0' INT TERM

# 启动开发服务器
npm run dev

# 如果到达这里，说明服务器正常退出
log "✅ Development servers stopped" "$GREEN"
