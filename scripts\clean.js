#!/usr/bin/env node

/**
 * 跨平台清理脚本
 * 清理构建缓存、临时文件和输出目录
 */

import { rmSync, existsSync } from 'fs';
import { platform } from 'os';
import { join } from 'path';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取命令行参数
const args = process.argv.slice(2);
const cacheOnly = args.includes('--cache-only');
const cleanAll = args.includes('--all');

// 定义要清理的目录和文件
const CLEAN_TARGETS = {
  // 缓存目录
  cache: [
    'node_modules/.vite',
    'node_modules/.cache',
    '.vite',
    '.turbo',
    '.next',
    '.nuxt',
    'dist-ssr'
  ],
  
  // 构建输出目录
  build: [
    'dist',
    'build',
    'out'
  ],
  
  // 临时文件
  temp: [
    '*.tmp',
    '*.temp',
    '.DS_Store',
    'Thumbs.db',
    '*.log'
  ],
  
  // 开发依赖（仅在 --all 模式下清理）
  dependencies: [
    'node_modules'
  ]
};

/**
 * 安全删除文件或目录
 */
function safeRemove(path) {
  try {
    if (existsSync(path)) {
      rmSync(path, { 
        recursive: true, 
        force: true,
        maxRetries: 3,
        retryDelay: 100
      });
      log(`✅ Removed: ${path}`, 'green');
      return true;
    } else {
      log(`⚪ Not found: ${path}`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`❌ Failed to remove ${path}: ${error.message}`, 'red');
    return false;
  }
}

/**
 * 清理指定类型的文件
 */
function cleanTargets(targets, description) {
  log(`\n🧹 Cleaning ${description}...`, 'cyan');
  
  let removedCount = 0;
  let totalCount = 0;
  
  for (const target of targets) {
    totalCount++;
    
    // 处理通配符模式（简单实现）
    if (target.includes('*')) {
      // 这里可以扩展为更复杂的通配符处理
      log(`⚪ Skipping wildcard pattern: ${target}`, 'yellow');
      continue;
    }
    
    if (safeRemove(target)) {
      removedCount++;
    }
  }
  
  log(`📊 ${description}: ${removedCount}/${totalCount} items removed`, 'blue');
}

/**
 * 获取目录大小（简单估算）
 */
function getDirectoryInfo(path) {
  try {
    if (existsSync(path)) {
      return { exists: true, path };
    }
    return { exists: false, path };
  } catch {
    return { exists: false, path };
  }
}

/**
 * 显示清理前的状态
 */
function showPreCleanStatus() {
  log('📋 Pre-clean Status:', 'cyan');
  
  const allTargets = [
    ...CLEAN_TARGETS.cache,
    ...CLEAN_TARGETS.build,
    ...(cleanAll ? CLEAN_TARGETS.dependencies : [])
  ];
  
  for (const target of allTargets) {
    const info = getDirectoryInfo(target);
    const status = info.exists ? '📁' : '⚪';
    const color = info.exists ? 'yellow' : 'reset';
    log(`   ${status} ${target}`, color);
  }
}

/**
 * 显示清理后的摘要
 */
function showSummary() {
  log('\n📊 Cleanup Summary:', 'cyan');
  log(`   Platform: ${platform()}`, 'blue');
  log(`   Mode: ${cacheOnly ? 'Cache only' : cleanAll ? 'Full cleanup' : 'Standard'}`, 'blue');
  
  // 检查剩余的重要目录
  const importantDirs = ['node_modules', 'dist', 'src'];
  for (const dir of importantDirs) {
    const exists = existsSync(dir);
    const status = exists ? '✅' : '❌';
    const color = exists ? 'green' : 'red';
    log(`   ${status} ${dir}`, color);
  }
}

/**
 * 主清理函数
 */
function main() {
  log('🧹 Huitong Material Cleanup Tool', 'bright');
  log('=' .repeat(40), 'blue');
  
  showPreCleanStatus();
  
  if (cacheOnly) {
    log('\n🎯 Cache-only cleanup mode', 'yellow');
    cleanTargets(CLEAN_TARGETS.cache, 'cache files');
  } else if (cleanAll) {
    log('\n🎯 Full cleanup mode (including node_modules)', 'yellow');
    cleanTargets(CLEAN_TARGETS.cache, 'cache files');
    cleanTargets(CLEAN_TARGETS.build, 'build files');
    cleanTargets(CLEAN_TARGETS.dependencies, 'dependencies');
    
    log('\n💡 After full cleanup, run "npm install" to restore dependencies', 'yellow');
  } else {
    log('\n🎯 Standard cleanup mode', 'yellow');
    cleanTargets(CLEAN_TARGETS.cache, 'cache files');
    cleanTargets(CLEAN_TARGETS.build, 'build files');
  }
  
  showSummary();
  
  log('\n' + '='.repeat(40), 'blue');
  log('✅ Cleanup completed', 'green');
}

// 错误处理
process.on('uncaughtException', (error) => {
  log(`❌ Uncaught exception: ${error.message}`, 'red');
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  log(`❌ Unhandled rejection: ${reason}`, 'red');
  process.exit(1);
});

// 运行主函数
main();
