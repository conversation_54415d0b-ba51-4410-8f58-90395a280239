import fs from 'fs/promises';
import path from 'path';

const localStorageDir = path.resolve('./local_blob_storage');

async function ensureDirExists() {
  try {
    await fs.mkdir(localStorageDir, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') {
      throw err;
    }
  }
}

async function list() {
  await ensureDirExists();
  const files = await fs.readdir(localStorageDir);
  return files.map(filename => ({
    url: `file://${path.join(localStorageDir, filename)}`,
    pathname: filename,
  }));
}

async function del(pathname) {
  const filePath = path.join(localStorageDir, pathname);
  try {
    await fs.unlink(filePath);
  } catch (err) {
    if (err.code !== 'ENOENT') {
      throw err;
    }
  }
}

async function copy(fromPath, toPath) {
  const fromFilePath = path.join(localStorageDir, fromPath);
  const toFilePath = path.join(localStorageDir, toPath);
  await ensureDirExists();
  await fs.copyFile(fromFilePath, toFilePath);
}

export { list, del, copy };
