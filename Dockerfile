# 使用官方的 Node.js 20 Alpine 镜像作为构建环境，它体积小且安全
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装所有依赖（包括开发依赖，因为构建前端需要它们）
RUN npm install

# 复制项目的所有源代码
COPY . .

# 运行 Prisma generate 来生成 Prisma Client
RUN npx prisma generate

# 运行构建命令，创建前端的生产版本
# 这会生成一个 /app/dist 目录
RUN npm run build

# --- 第二阶段：创建最终的生产镜像 ---

# 同样使用轻量的 Node.js 20 Alpine 镜像
FROM node:20-alpine AS production

WORKDIR /app

# 从构建器阶段复制 package.json 和 package-lock.json
COPY --from=builder /app/package*.json ./

# 只安装生产环境所需的依赖
RUN npm install --omit=dev

# 从构建器阶段复制已生成的 Prisma schema 和后端代码
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/backend ./backend

# 从构建器阶段复制构建好的前端静态文件
COPY --from=builder /app/dist ./dist

# 暴露后端服务运行的端口
# 注意：这个端口只在 Docker 网络内部可见，Nginx 会处理外部访问
EXPOSE 3001

# 设置环境变量，确保服务器以生产模式启动
ENV NODE_ENV=production
ENV PORT=3001

# 启动后端服务的命令
CMD ["node", "backend/server.mjs"]
