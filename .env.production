# 生产环境配置文件
# 请根据实际部署环境修改以下配置

# 数据库配置
POSTGRES_USER=postgres
POSTGRES_PASSWORD=jdWf45+Q7gksJMhwb/C57i2x9m+QfeI1YhPe7OAr8qU=
POSTGRES_DB=huitong_material
POSTGRES_HOST=db
POSTGRES_PORT=5432

# 数据库连接URL (Prisma格式)
POSTGRES_PRISMA_URL=postgresql://postgres:jdWf45+Q7gksJMhwb/C57i2x9m+QfeI1YhPe7OAr8qU=@db:5432/huitong_material?schema=public
POSTGRES_URL_NON_POOLING=postgresql://postgres:jdWf45+Q7gksJMhwb/C57i2x9m+QfeI1YhPe7OAr8qU=@db:5432/huitong_material?schema=public

# 应用配置
NODE_ENV=production
PORT=3001

# 文件上传配置
UPLOAD_DIR=./backend/uploads
MAX_FILE_SIZE=52428800
# 50MB in bytes

# 安全配置
JWT_SECRET=98a1Ze6Ii5q3PkieVGmG9EqF+J0dMlw674NB3puSG8M=
COOKIE_SECRET=37bfUWAg+/2/zkgb0a8ShQ/M26BKdlVLqyu3PIkc0nY=

# 跨域配置
CORS_ORIGIN=*

# 日志配置
LOG_LEVEL=info

# 阿里云配置（如果需要）
# ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_here
# ALIYUN_ACCESS_KEY_SECRET=your_aliyun_secret_here
# ALIYUN_REGION=cn-hangzhou

# 监控和健康检查
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/api/health

# 性能配置
MAX_REQUEST_SIZE=50mb
REQUEST_TIMEOUT=30000

# 缓存配置
CACHE_TTL=3600
STATIC_CACHE_TTL=86400
