version: '3.8'

# 定义所有服务
services:
  # 数据库服务 (PostgreSQL)
  db:
    image: postgres:15-alpine
    container_name: huitong_db
    restart: always
    volumes:
      # 将数据库数据持久化到宿主机的命名卷中，防止数据丢失
      - postgres_data:/var/lib/postgresql/data
    environment:
      # 这些变量将从 .env.prod 文件中读取
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 应用程序服务 (Node.js Backend)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: huitong_app
    restart: always
    depends_on:
      - db
    environment:
      - NODE_ENV=production
      - POSTGRES_PRISMA_URL=${POSTGRES_PRISMA_URL}
      - PORT=3001
    volumes:
      # Mount the shared volume for uploads
      - uploads_data:/app/backend/uploads
      # Anonymous volume to ensure node_modules installed in the container are used
      - /app/node_modules
    networks:
      - app-network

  # Web 服务器 (Nginx)
  nginx:
    image: nginx:stable-alpine
    container_name: huitong_nginx
    restart: always
    ports:
      # 将服务器的 80 端口映射到容器的 80 端口 (HTTP)
      - "80:80"
      # 将服务器的 443 端口映射到容器的 443 端口 (HTTPS, 为未来配置SSL做准备)
      - "443:443"
    volumes:
      # 挂载自定义的 Nginx 配置文件 (只读)
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      # 从 app 服务中挂载构建好的前端静态文件
      - ./dist:/usr/share/nginx/html:ro
      # Mount the shared volume for uploads, read-only for security
      - uploads_data:/var/www/uploads:ro
      # Volumes for Let's Encrypt SSL certificates
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    depends_on:
      - app
    networks:
      - app-network

# 定义命名卷
volumes:
  postgres_data:
  uploads_data:
  app_dist:
  certbot_conf:
  certbot_www:

# 定义自定义网络
networks:
  app-network:
    driver: bridge