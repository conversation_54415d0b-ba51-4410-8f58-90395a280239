import dotenv from 'dotenv';
import { list, del } from './local-storage.mjs';
import { PrismaClient } from '@prisma/client';

// Load environment variables from .env.local first, then .env
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

const getDbUrl = () => {
  const dbUrl = process.env.POSTGRES_PRISMA_URL;
  if (dbUrl && dbUrl.includes('vercel-storage.com')) {
    try {
      const url = new URL(dbUrl);
      if (!url.searchParams.has('pgbouncer')) {
        url.searchParams.set('pgbouncer', 'true');
      }
      if (!url.searchParams.has('sslmode')) {
        url.searchParams.set('sslmode', 'require');
      }
      return url.toString();
    } catch (e) {
      console.error("Invalid POSTGRES_PRISMA_URL:", e);
      return dbUrl;
    }
  }
  return dbUrl;
};

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: getDbUrl(),
    },
  },
});

async function cleanupOrphanedBlobs() {
  console.log('🚀 Starting Blob cleanup process...');

  try {
    // 1. Fetch all valid file URLs from the database
    console.log('🔍 Fetching valid file URLs from the database...');
    const models = await prisma.model.findMany({
      select: { filePath: true, thumbnailPath: true },
    });
    const materials = await prisma.material.findMany({
      select: { thumbnailPath: true },
    });

    const validUrls = new Set();
    models.forEach(model => {
      if (model.filePath) validUrls.add(model.filePath);
      if (model.thumbnailPath) validUrls.add(model.thumbnailPath);
    });
    materials.forEach(material => {
      if (material.thumbnailPath) validUrls.add(material.thumbnailPath);
    });

    console.log(`✅ Found ${validUrls.size} valid file URLs in the database.`);

    // 2. List all blobs from Vercel Blob storage
    console.log('☁️  Listing all files from Vercel Blob storage...');
    const { blobs } = await list();
    console.log(`✅ Found ${blobs.length} total files in Blob storage.`);

    // 3. Identify orphaned blobs
    const orphanedBlobs = blobs.filter(blob => !validUrls.has(blob.url));

    if (orphanedBlobs.length === 0) {
      console.log('🎉 No orphaned files found. Your Blob storage is clean!');
      return;
    }

    console.log(`🗑️  Found ${orphanedBlobs.length} orphaned files to delete:`);
    orphanedBlobs.forEach(blob => console.log(`   - ${blob.pathname} (${(blob.size / 1024).toFixed(2)} KB)`));

    // 4. Delete orphaned blobs
    console.log('\nDeleting orphaned files...');
    const deletePromises = orphanedBlobs.map(blob => del(blob.url));
    await Promise.all(deletePromises);

    console.log('✅ Cleanup complete. All orphaned files have been deleted.');

  } catch (error) {
    console.error('❌ An error occurred during the cleanup process:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed.');
  }
}

cleanupOrphanedBlobs();
