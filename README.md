# Huitong Material

一个现代化的3D材质渲染和管理平台，支持GLB/GLTF模型的实时渲染、材质编辑和预览。

## ✨ 特性

- 🎨 **实时3D渲染** - 基于Three.js的高性能3D渲染引擎
- 🔧 **材质编辑** - 直观的材质属性调节界面
- 📁 **模型管理** - 支持GLB/GLTF格式的3D模型上传和管理
- 🎯 **响应式设计** - 适配桌面和移动设备
- 🌐 **跨平台支持** - Windows、macOS、Linux全平台兼容
- ⚡ **快速启动** - 一键环境配置和启动脚本

## 🚀 快速开始

### 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- 现代浏览器（支持WebGL 2.0）

### 一键安装

```bash
# 克隆项目
git clone https://github.com/your-username/huitong-material.git
cd huitong-material

# 自动环境配置
npm run setup

# 启动开发服务器
npm run dev:safe
```

### 手动安装

如果自动安装遇到问题，可以按以下步骤手动配置：

```bash
# 1. 安装依赖
npm install

# 2. 生成数据库客户端
npm run db:generate

# 3. 创建环境配置文件
cp .env .env.local

# 4. 编辑环境变量（见下方配置说明）
nano .env.local

# 5. 检查环境配置
npm run check:env

# 6. 启动开发服务器
npm run dev:safe
```

## ⚙️ 环境配置

创建 `.env.local` 文件并配置以下环境变量：

```env
# 数据库配置
POSTGRES_PRISMA_URL="your_postgres_connection_string"
POSTGRES_URL_NON_POOLING="your_postgres_direct_connection_string"

# 文件存储配置
BLOB_READ_WRITE_TOKEN="your_vercel_blob_token"

# 开发配置
NODE_ENV=development
PORT=3001
```

### 获取配置值

1. **数据库配置**: 使用 [Neon](https://neon.tech/) 或其他PostgreSQL服务
2. **文件存储**: 使用 [Vercel Blob](https://vercel.com/docs/storage/vercel-blob) 服务

## 📜 可用脚本

### 开发命令

```bash
npm run dev:safe      # 安全启动开发服务器（推荐）
npm run dev           # 标准启动开发服务器
npm run dev:frontend  # 仅启动前端服务器
npm run dev:backend   # 仅启动后端服务器
```

### 构建和部署

```bash
npm run build         # 构建生产版本
npm run start         # 启动生产服务器
npm run start:prod    # 启动生产服务器（带健康检查）
npm run preview       # 预览构建结果
```

### 数据库管理

```bash
npm run db:generate   # 生成Prisma客户端
npm run db:push       # 推送数据库架构变更
npm run db:studio     # 打开Prisma Studio
```

### 代码质量

```bash
npm run lint          # 检查代码规范
npm run lint:fix      # 自动修复代码问题
npm run lint:unused   # 检查未使用的代码
```

### 环境管理

```bash
npm run setup         # 自动环境配置
npm run check:env     # 检查环境配置
npm run clean         # 清理缓存文件
npm run clean:all     # 完全清理（包括node_modules）
```

## 🏗️ 项目结构

```
huitong-material/
├── src/                    # 前端源代码
│   ├── components/         # React组件
│   ├── pages/             # 页面组件
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   └── styles/            # 样式文件
├── backend/               # 后端服务器
│   └── server.mjs         # Express服务器
├── scripts/               # 构建和部署脚本
│   ├── start-dev.js       # 开发启动脚本
│   ├── start-prod.js      # 生产启动脚本
│   ├── check-env.js       # 环境检查脚本
│   ├── setup-env.js       # 环境配置脚本
│   └── clean.js           # 清理脚本
├── prisma/                # 数据库架构
│   └── schema.prisma      # Prisma架构文件
└── public/                # 静态资源
```

## 🔧 开发指南

### 添加新功能

1. 在 `src/components/` 中创建新组件
2. 在 `src/pages/` 中创建新页面
3. 在 `src/services/` 中添加API调用
4. 更新路由配置

### 样式规范

- 使用 `src/styles/variables.css` 中定义的CSS变量
- 遵循组件化设计原则
- 保持响应式设计

### 3D模型要求

- 支持格式：GLB、GLTF
- 最大文件大小：50MB
- 材质命名：以 'Editable' 开头的材质可在界面中编辑

## 🚀 部署

### Vercel部署

1. 连接GitHub仓库到Vercel
2. 配置环境变量
3. 自动部署

### 手动部署

```bash
# 构建项目
npm run build

# 启动生产服务器
npm run start:prod
```

## 🐛 故障排除

### 常见问题

**端口被占用**
```bash
# 检查端口使用情况
npm run check:env

# 或手动杀死进程
lsof -ti:3001 | xargs kill -9  # macOS/Linux
netstat -ano | findstr :3001   # Windows
```

**数据库连接失败**
```bash
# 检查环境变量配置
npm run check:env

# 测试数据库连接
npm run db:studio
```

**依赖安装失败**
```bash
# 清理并重新安装
npm run clean:all
npm install
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 3D模型渲染功能
- 材质编辑系统
- 跨平台支持

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交Issue或联系开发团队。
